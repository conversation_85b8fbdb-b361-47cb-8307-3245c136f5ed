# Jira Integration Fix Documentation - v2

## Issue Description

The Jira handler was not being triggered after DNS operations completion in v2 because:

1. **Missing notify statements**: No tasks were notifying the "Update Jira Ticket" handler
2. **No conditional logic**: The handler would fail when run manually from AAP without Jira variables
3. **Complex v2 architecture**: Dynamic host addition and multi-play structure required careful variable handling

## Root Cause Analysis

### Original Problem
- Handler defined in `main.yml` but never triggered
- No distinction between Jira-triggered jobs vs manual AAP execution
- Missing required variables (`var_sr_number`, `var_grid_id`, `var_row_id`) would cause failures
- v2 specific: Variables needed to be accessed via `hostvars['localhost']` in second play

### Variables Required for Jira Integration
- `var_sr_number`: Service Request number (e.g., "SCR-12345")
- `var_grid_id`: Jira grid identifier
- `var_row_id`: Specific row ID in the Jira grid
- `aap_url`: AAP base URL for job links
- `tower_job_id`: Current job ID for linking

## Solution Implemented

### 1. Added Notify Statements
**File**: `manage-dns/tasks/dns-operations.yml`

```yaml
# Primary trigger - Enhanced email notification task
- name: Send enhanced email notification with Instance Group information
  community.general.mail:
    # ... email configuration with v2 enhancements ...
  notify: Update Jira Ticket

# Fallback trigger - Ensures handler runs even if email fails
- name: Trigger Jira ticket update after DNS v2 operations completion
  ansible.builtin.debug:
    msg: "DNS v2 operations completed. Triggering Jira ticket update for {{ hostvars['localhost']['var_sr_number'] | default('SCR-XXXXX') }}"
  delegate_to: localhost
  run_once: true
  notify: Update Jira Ticket
  when: 
    - hostvars['localhost']['var_sr_number'] is defined 
    - hostvars['localhost']['var_sr_number'] != ""
    - hostvars['localhost']['var_sr_number'] != "SCR-XXXXX"
    - hostvars['localhost']['var_grid_id'] is defined
    - hostvars['localhost']['var_row_id'] is defined
```

### 2. Enhanced Handler Logic for v2
**File**: `manage-dns/handlers/update-sr-uat.yml`

```yaml
# Smart detection of Jira vs Manual execution
- name: Check if all required Jira variables are available for v2
  ansible.builtin.set_fact:
    jira_update_enabled: >-
      {{
        (var_sr_number is defined and var_sr_number != '' and var_sr_number != 'SCR-XXXXX') and
        (var_grid_id is defined and var_grid_id != '') and
        (var_row_id is defined and var_row_id != '')
      }}

# Enhanced Jira update with v2 information
- name: Update grid row in a SR ticket with v2 information
  ansible.builtin.uri:
    # ... Jira API call with Instance Group and ADMT server info ...
    body: |
      {
        "rows":[
          {
            "rowId":"{{ var_row_id }}",
            "columns":{
              "remark": "DNS v2 - {{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/ - ADMT: {{ hostvars[groups['admt_servers'][0]]['inventory_hostname'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }} - IG: {{ hostvars[groups['admt_servers'][0]]['instance_group'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }}"
            }
          }
        ]
      }
  when: jira_update_enabled
```

## v2 Specific Enhancements

### 1. Variable Access Pattern
- Uses `hostvars['localhost']['var_name']` to access variables from first play
- Handles dynamic host addition scenario properly
- Maintains compatibility with AAP Instance Group routing

### 2. Enhanced Jira Comments
- Includes ADMT server information
- Shows Instance Group assignment
- Provides v2-specific context in ticket updates

### 3. Multi-Play Architecture Support
- Handler defined in first play (localhost)
- Triggered from second play (ADMT server)
- Variables properly passed between plays

## How It Works

### Jira-Triggered Execution (v2)
1. **Jira provides all required variables to first play**:
   - `var_sr_number`: "SCR-12345"
   - `var_grid_id`: "grid123"
   - `var_row_id`: "row456"

2. **Variables passed to second play via add_host**
3. **Handler logic in second play**:
   - Accesses variables via `hostvars['localhost']`
   - `jira_update_enabled` = `true`
   - Jira API call executes with v2 enhancements
   - Ticket updated with AAP job link + Instance Group info

### Manual AAP Execution (v2)
1. **Missing or default Jira variables**:
   - `var_sr_number`: "SCR-XXXXX" (default) or undefined
   - `var_grid_id`: undefined
   - `var_row_id`: undefined

2. **Handler logic**:
   - `jira_update_enabled` = `false`
   - Jira API call skipped
   - No failure, job completes successfully

## Testing Scenarios

### Test 1: Jira-Triggered Job (v2)
```bash
# Variables provided by Jira to first play
var_sr_number: "SCR-12345"
var_grid_id: "12345"
var_row_id: "67890"
domain: "healthgrp.com.sg"

# Expected Result: 
# - ADMT server dynamically added
# - DNS operations executed
# - Jira ticket updated with v2 info
```

### Test 2: Manual AAP Execution (v2)
```bash
# Minimal variables for DNS operation
domain: "healthgrp.com.sg"
hostname: "testserver"
var_action: "verify"

# Expected Result: 
# - DNS operations complete successfully
# - No Jira update attempted
# - No failures
```

## Benefits of v2 Implementation

1. **Enterprise-Grade Routing**: Works with AAP Instance Groups
2. **Dynamic Server Selection**: Automatically selects correct ADMT server
3. **Enhanced Logging**: Includes Instance Group and ADMT server info
4. **Robust Operation**: Works for both Jira and manual execution
5. **Network Segmentation**: Supports HDC1/HDC2 routing
6. **Backward Compatible**: Maintains all v1 functionality

## Monitoring and Troubleshooting

### v2 Specific Log Messages
```
"Successfully added [ADMT_SERVER] to Instance Group [IG_NAME]"
"DNS v2 operations completed. Triggering Jira ticket update"
"Jira Update Status: ENABLED/DISABLED"
```

### Common v2 Issues
1. **Variable access**: Use `hostvars['localhost']['var_name']` in second play
2. **Instance Group routing**: Check ADMT server to IG mapping
3. **Dynamic host addition**: Verify add_host task execution

## Implementation Notes

- Compatible with both UAT and Production environments
- Supports all v2 enhancements (Instance Groups, dynamic routing)
- Maintains fail-safe operation for manual execution
- Enhanced Jira comments provide better operational visibility
