{#
   Enhanced Email Template for DNS Management v2 with AAP Instance Group Routing
   This template provides comprehensive reporting for DNS operations including:
   - A record status and details
   - Intelligent PTR zone detection results
   - PTR management operation status
   - AAP Instance Group routing information
   - Enhanced error reporting and troubleshooting information
   - Performance metrics and timing information

   Author: CES Operational Excellence Team
   Contributor: <PERSON> (7409)
   Version: 2.0 - Enhanced with AAP Instance Group Routing
#}

Dear {{ team_name }},<br><br>

Please be informed that the following VM Lifecycle Non-Windows DNS A record is triggered to <b><u>{{ dns_records.action }}</u></b> via <b>{{ var_ticket }}</b>.<br><br>

<b><u>AAP Instance Group Routing Information (v2)</u></b><br>
<div style="background-color: #e8f5e8; padding: 10px; border-left: 4px solid #4caf50;">
  <b style="color: #2e7d32;">🚀 Enhanced v2 Features Active</b><br>
  - <b>ADMT Server:</b> {{ dns_records.admt_server if dns_records.admt_server else 'Not specified' }}<br>
  - <b>Instance Group:</b> {{ dns_records.instance_group if dns_records.instance_group else 'Not specified' }}<br>
  - <b>DC Location:</b> {{ dns_records.dc_location if dns_records.dc_location else 'Not specified' }}<br>
  - <b>Execution Method:</b> Dynamic ADMT server selection with Instance Group routing<br>
</div><br>

<b><u>DNS A Record Status</u></b><br>
- <b>A Record Status:</b> {{ dns_records.status }}<br>
- <b>Domain:</b> {{ dns_records.domain }}<br>
- <b>DNS Server:</b> {{ dns_records.dns_server }}<br>
- <b>Hostname:</b> {{ dns_records.hostname }}<br>
- <b>IP Address:</b> {{ dns_records.ip_address if dns_records.ip_address else 'Nil' }}<br>
- <b>TTL:</b> {{ dns_records.ttl if dns_records.ttl else 'Nil' }}<br><br>

<b><u>DNS PTR Record Status (Enhanced)</u></b><br>
- <b>PTR Management Enabled:</b>
  {% if dns_records.ptr_management_enabled is defined %}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      <span style="color: green;"><b>✅ YES</b></span> - Intelligent PTR zone detection active
    {% else %}
      <span style="color: orange;"><b>⚠️ NO</b></span> - Legacy mode (A records only)
    {% endif %}
  {% else %}
    <span style="color: green;"><b>✅ YES</b></span> - Default enabled
  {% endif %}<br>
- <b>PTR DNS Server:</b> {{ dns_records.ptr_dns_server if dns_records.ptr_dns_server else 'Same as A record DNS server' }}<br>
- <b>PTR Zone Detection Result:</b>
  {% if dns_records.ptr_zone_detected and dns_records.ptr_zone_detected != 'Nil' and dns_records.ptr_zone_detected != 'None' %}
    <span style="color: green;"><b>{{ dns_records.ptr_zone_detected }}</b></span>
    {% if 'in-addr.arpa' in dns_records.ptr_zone_detected %}
      {% if dns_records.ptr_zone_detected.split('.') | length == 4 %}
        (3-octet zone - Most specific)
      {% elif dns_records.ptr_zone_detected.split('.') | length == 3 %}
        (2-octet zone - Medium specificity)
      {% elif dns_records.ptr_zone_detected.split('.') | length == 2 %}
        (1-octet zone - Least specific)
      {% endif %}
    {% endif %}
  {% else %}
    <span style="color: red;"><b>❌ No suitable PTR zones found</b></span>
  {% endif %}<br>
- <b>PTR Operation Status:</b>
  {% if dns_records.ptr_operation_status and dns_records.ptr_operation_status != 'Nil' %}
    {% if 'successfully' in dns_records.ptr_operation_status or 'created' in dns_records.ptr_operation_status or 'removed' in dns_records.ptr_operation_status %}
      <span style="color: green;"><b>✅ {{ dns_records.ptr_operation_status }}</b></span>
    {% elif 'failed' in dns_records.ptr_operation_status or 'error' in dns_records.ptr_operation_status %}
      <span style="color: red;"><b>❌ {{ dns_records.ptr_operation_status }}</b></span>
    {% elif 'disabled' in dns_records.ptr_operation_status or 'skipped' in dns_records.ptr_operation_status %}
      <span style="color: orange;"><b>⚠️ {{ dns_records.ptr_operation_status }}</b></span>
    {% else %}
      <b>{{ dns_records.ptr_operation_status }}</b>
    {% endif %}
  {% else %}
    <span style="color: gray;"><b>ℹ️ No PTR operation performed</b></span>
  {% endif %}<br>
- <b>PTR Record Value:</b> {{ dns_records.ptr_record if dns_records.ptr_record and dns_records.ptr_record != 'Nil' else 'No PTR record' }}<br><br>

<b><u>Operation Summary</u></b><br>
{% set operation_success = true %}
{% if dns_records.errors and dns_records.errors != '' %}
  {% set operation_success = false %}
{% endif %}

{% if operation_success %}
  <span style="color: green; font-size: 16px;"><b>✅ OPERATION COMPLETED SUCCESSFULLY</b></span><br>
  {% if dns_records.action == 'verify' %}
    - DNS record verification completed via {{ dns_records.admt_server if dns_records.admt_server else 'ADMT server' }}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      with intelligent PTR zone detection
    {% endif %}
  {% elif dns_records.action == 'add' %}
    - DNS A record {{ 'created' if 'Added' in dns_records.status else 'verified' }} via {{ dns_records.instance_group if dns_records.instance_group else 'Instance Group' }}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      {% if dns_records.ptr_zone_detected and dns_records.ptr_zone_detected != 'Nil' and dns_records.ptr_zone_detected != 'None' %}
        - PTR record managed in optimal zone: {{ dns_records.ptr_zone_detected }}
      {% else %}
        - PTR zone detection completed (no suitable zones found)
      {% endif %}
    {% else %}
      - PTR management disabled (legacy mode)
    {% endif %}
  {% elif dns_records.action == 'remove' %}
    - DNS A record {{ 'removed' if 'Removed' in dns_records.status else 'verified for removal' }} via {{ dns_records.instance_group if dns_records.instance_group else 'Instance Group' }}
    {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
      - PTR records safely processed (only matching records affected)
    {% else %}
      - PTR management disabled (legacy mode)
    {% endif %}
  {% endif %}
{% else %}
  <span style="color: red; font-size: 16px;"><b>❌ OPERATION COMPLETED WITH ERRORS</b></span><br>
  - Please review the error details below and contact support if needed
{% endif %}<br><br>

<b><u>DNS Processing Errors & Warnings</u></b><br>
{% if dns_records.errors and dns_records.errors != '' %}
  <div style="background-color: #ffebee; padding: 10px; border-left: 4px solid #f44336;">
    <b style="color: #d32f2f;">⚠️ Error Details:</b><br>
    <pre style="color: #d32f2f; font-family: monospace;">{{ dns_records.errors }}</pre>
  </div>
{% else %}
  <span style="color: green;"><b>✅ No Processing Errors</b></span>
{% endif %}<br><br>

<b><u>Enhanced v2 Architecture Information</u></b><br>
<div style="background-color: #e3f2fd; padding: 10px; border-left: 4px solid #2196f3;">
  <b style="color: #1976d2;">🚀 DNS Management v2 Features</b><br>
  This operation utilized the enhanced v2 architecture with the following capabilities:<br>
  • <b>Dynamic ADMT Selection</b>: Automatically selected optimal ADMT server based on domain<br>
  • <b>Instance Group Routing</b>: AAP routing via {{ dns_records.instance_group if dns_records.instance_group else 'appropriate Instance Group' }}<br>
  • <b>Network Segmentation</b>: Intelligent routing to {{ dns_records.dc_location if dns_records.dc_location else 'appropriate DC' }} network segment<br>
  • <b>Enterprise Automation</b>: Runtime variable integration for scalable operations<br>
  • <b>Enhanced Logging</b>: Comprehensive execution tracking and monitoring<br>
  {% if dns_records.ptr_management_enabled == true or dns_records.ptr_management_enabled == 'True' %}
  • <b>Intelligent PTR Management</b>: 3-tier zone detection with hierarchical fallback<br>
  • <b>Safe PTR Operations</b>: Error isolation and zone preservation<br>
  {% endif %}
</div><br>

{% if dns_records.errors and dns_records.errors != '' %}
<b><u>Troubleshooting Guidance</u></b><br>
<div style="background-color: #fff3e0; padding: 10px; border-left: 4px solid #ff9800;">
  <b style="color: #f57c00;">🔧 Next Steps for Error Resolution:</b><br>

  {% if 'Instance Group' in dns_records.errors or 'routing' in dns_records.errors %}
    <b>Instance Group Routing Issues:</b><br>
    • Verify AAP Instance Group {{ dns_records.instance_group if dns_records.instance_group else 'configuration' }} has network access to target servers<br>
    • Check network connectivity between Instance Group and {{ dns_records.dc_location if dns_records.dc_location else 'target DC' }}<br>
    • Contact AAP administrators for Instance Group troubleshooting<br><br>
  {% endif %}

  {% if 'PTR' in dns_records.errors %}
    <b>PTR-Related Issues:</b><br>
    • Check if PTR zones are accessible from {{ dns_records.admt_server if dns_records.admt_server else 'ADMT servers' }}<br>
    • Verify network connectivity to PTR DNS servers via {{ dns_records.instance_group if dns_records.instance_group else 'Instance Group' }}<br>
    • Consider running with <code>manage_ptr: false</code> for A record only operations<br>
    • PTR failures don't affect A record operations (error isolation active)<br><br>
  {% endif %}

  <b>v2 Architecture Troubleshooting:</b><br>
  • Review AAP job logs for Instance Group routing details<br>
  • Verify ADMT server {{ dns_records.admt_server if dns_records.admt_server else 'selection' }} is appropriate for domain<br>
  • Check network policies for {{ dns_records.dc_location if dns_records.dc_location else 'DC' }} access<br>
  • Contact CES Operational Excellence Team for v2 architecture support<br>
</div><br>
{% endif %}

<b><u>Technical Details & Raw Output</u></b><br>
<details>
  <summary><b>Click to expand raw PowerShell output</b></summary>
  <div style="background-color: #f5f5f5; padding: 10px; margin: 10px 0; border: 1px solid #ddd;">
    <pre style="font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">{{ raw_text | e }}</pre>
  </div>
</details><br>

<b><u>Job Information & Links</u></b><br>
- <b>AAP Job Link:</b> <a href="{{ aap_job_link }}" style="color: #1976d2;">{{ aap_job_link }}</a><br>
- <b>Ticket Reference:</b> {{ var_ticket }}<br>
- <b>Execution Time:</b> {{ dns_records.execution_timestamp if dns_records.execution_timestamp is defined else ansible_date_time.iso8601 if ansible_date_time is defined else 'Not available' }}<br>
- <b>ADMT Server:</b> {{ dns_records.admt_server if dns_records.admt_server else 'Not specified' }}<br>
- <b>Instance Group:</b> {{ dns_records.instance_group if dns_records.instance_group else 'Not specified' }}<br><br>

---<br><br>

<b><u>Support & Contact Information</u></b><br>
- <b>Primary Support:</b> CES Operational Excellence Team<br>
- <b>Email:</b> <EMAIL><br>
- <b>Documentation:</b> DNS Management Project (vmlc-services-dns-v2)<br>
- <b>Emergency Escalation:</b> Follow standard IT support procedures<br><br>

<b><u>Enhanced v2 Features Information</u></b><br>
<span style="color: #1976d2;"><b>🚀 DNS Management v2.0 - AAP Instance Group Routing</b></span><br>
This notification was generated using the enhanced DNS management system v2 with dynamic ADMT server selection and AAP Instance Group routing capabilities.<br><br>

Regards,<br>
<b>CES OPERATIONAL EXCELLENCE TEAM</b><br>
<i>DNS Management Automation v2.0 - Enhanced with AAP Instance Group Routing</i><br><br>

<hr style="border: 1px solid #ddd;"><br>

<b>📋 INFORMATION CLASSIFICATION</b><br>
[ ] Unclassified, Non-Sensitive<br>
[ ] Restricted, Non-Sensitive<br>
[x] Restricted, Sensitive (Normal)<br>
[ ] Restricted, Sensitive (High)<br><br>

<b>⚠️ IMPORTANT NOTICES</b><br>
• This email contains DNS infrastructure and AAP routing information<br>
• Forward only to authorized personnel<br>
• Report any DNS-related issues immediately<br>
• Verify DNS changes before making dependent system modifications<br><br>

<div style="background-color: #f5f5f5; padding: 8px; border: 1px solid #ddd; font-size: 11px; color: #666;">
  <b>🤖 Automated Notification System v2</b><br>
  This is an auto-generated email from the DNS Management Automation system v2.<br>
  Please do not reply to this email. For support, contact the CES Operational Excellence Team.<br>
  Generated: {{ dns_records.execution_timestamp if dns_records.execution_timestamp is defined else ansible_date_time.iso8601 if ansible_date_time is defined else 'Unknown' }} |
  Job ID: {{ tower_job_id if tower_job_id is defined else 'N/A' }} |
  Version: 2.0.0 | ADMT: {{ dns_records.admt_server if dns_records.admt_server else 'N/A' }}
</div>
