# AAP Execution Examples - DNS Management v2

## Overview

This document provides comprehensive examples of how to execute DNS Management v2 operations from Ansible Automation Platform (AAP) using extra variables. Each example demonstrates the enhanced Instance Group routing and dynamic ADMT server selection capabilities.

## Execution Flow Summary

```
AAP Job Launch (Extra Variables)
       ↓
Dynamic ADMT Server Selection (based on domain)
       ↓
Instance Group Routing Assignment
       ↓
Dynamic Host Addition (add_host module)
       ↓
DNS Operations on Selected ADMT Server
       ↓
Enhanced Email Notification with Routing Info
```

## 1. Verify Operations

### Example 1.1: Basic Verification - HDC2 Routing

**AAP Extra Variables:**
```json
{
  "var_action": "verify",
  "domain": "healthgrp.com.sg",
  "hostname": "testserver01",
  "var_sr_number": "SCR-12345",
  "var_environment": "production"
}
```

**Expected Execution Flow:**
1. **Domain Analysis**: `healthgrp.com.sg` → Maps to `HISADMTVPSEC05.healthgrp.com.sg`
2. **Instance Group Selection**: `HISADMTVPSEC05.healthgrp.com.sg` → `SYP_H_HPC_MGT_UNXWIN_HDC2`
3. **Dynamic Host Addition**: 
   ```yaml
   name: "HISADMTVPSEC05.healthgrp.com.sg"
   groups: ["admt_servers", "SYP_H_HPC_MGT_UNXWIN_HDC2"]
   dc_location: "HDC2"
   instance_group: "SYP_H_HPC_MGT_UNXWIN_HDC2"
   ```
4. **DNS Verification**: Check A and PTR records for `testserver01.healthgrp.com.sg`
5. **Email Notification**: Enhanced notification with HDC2 routing information

### Example 1.2: Basic Verification - HDC1 Routing

**AAP Extra Variables:**
```json
{
  "var_action": "verify",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "prodserver01",
  "var_sr_number": "SCR-12346"
}
```

**Expected Execution Flow:**
1. **Domain Analysis**: `hcloud.healthgrp.com.sg` → Maps to `HISADMTVPSEC06.hcloud.healthgrp.com.sg`
2. **Instance Group Selection**: `HISADMTVPSEC06.hcloud.healthgrp.com.sg` → `SYP_H_HPC_MGT_UNXWIN_HDC1`
3. **Dynamic Host Addition**: 
   ```yaml
   name: "HISADMTVPSEC06.hcloud.healthgrp.com.sg"
   groups: ["admt_servers", "SYP_H_HPC_MGT_UNXWIN_HDC1"]
   dc_location: "HDC1"
   instance_group: "SYP_H_HPC_MGT_UNXWIN_HDC1"
   ```

## 2. Add Operations

### Example 2.1: Add DNS Record - HDC1 Routing

**AAP Extra Variables:**
```json
{
  "var_action": "add",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "newprod01",
  "ipaddress": "*************",
  "ttl": "3600",
  "manage_ptr": "true",
  "var_sr_number": "SCR-12347",
  "var_environment": "production"
}
```

**Expected Execution Flow:**
1. **Domain Analysis**: `hcloud.healthgrp.com.sg` → Maps to `HISADMTVPSEC06.hcloud.healthgrp.com.sg`
2. **Instance Group Selection**: `HISADMTVPSEC06.hcloud.healthgrp.com.sg` → `SYP_H_HPC_MGT_UNXWIN_HDC1`
3. **Dynamic Host Addition**: HDC1 routing configuration
4. **DNS Operations**:
   - Create A record: `newprod01.hcloud.healthgrp.com.sg` → `*************`
   - Intelligent PTR zone detection and record creation
   - TTL: 3600 seconds
5. **Email Notification**: Success notification with HDC1 routing details

### Example 2.2: Add DNS Record - HDC2 Routing with Custom TTL

**AAP Extra Variables:**
```json
{
  "var_action": "add",
  "domain": "healthgrpexts.com.sg",
  "hostname": "specialserver01",
  "ipaddress": "************",
  "ttl": "7200",
  "manage_ptr": "true",
  "var_environment": "production",
  "var_sr_number": "SCR-12348"
}
```

**Expected Execution Flow:**
1. **Domain Analysis**: `healthgrpexts.com.sg` → Maps to `HISADMTVSSEC01.healthgrpexts.com.sg`
2. **Instance Group Selection**: `HISADMTVSSEC01.healthgrpexts.com.sg` → `SYP_H_HPC_MGT_UNXWIN_HDC2`
3. **Dynamic Host Addition**: HDC2 routing configuration
4. **DNS Operations**:
   - Create A record with custom TTL (7200 seconds)
   - Enhanced PTR management via HDC2 network segment
5. **Email Notification**: Success notification with HDC2 routing and custom TTL information

## 3. Remove Operations

### Example 3.1: Remove DNS Record - HDC1 Routing

**AAP Extra Variables:**
```json
{
  "var_action": "remove",
  "domain": "nhg.local",
  "hostname": "oldserver01",
  "manage_ptr": "true",
  "var_sr_number": "SCR-12349",
  "var_environment": "production"
}
```

**Expected Execution Flow:**
1. **Domain Analysis**: `nhg.local` → Maps to `HISADMTVPSEC11.nhg.local`
2. **Instance Group Selection**: `HISADMTVPSEC11.nhg.local` → `SYP_H_HPC_MGT_UNXWIN_HDC1`
3. **Dynamic Host Addition**: HDC1 routing configuration
4. **DNS Operations**:
   - Remove A record: `oldserver01.nhg.local`
   - Safe PTR record removal (only matching records)
   - Preserve PTR records pointing to different hostnames
5. **Email Notification**: Removal confirmation with HDC1 routing details

### Example 3.2: Remove DNS Record - HDC2 Routing, PTR Disabled

**AAP Extra Variables:**
```json
{
  "var_action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "legacyserver01",
  "manage_ptr": "false",
  "var_sr_number": "SCR-12350"
}
```

**Expected Execution Flow:**
1. **Domain Analysis**: `healthgrp.com.sg` → Maps to `HISADMTVPSEC05.healthgrp.com.sg`
2. **Instance Group Selection**: `HISADMTVPSEC05.healthgrp.com.sg` → `SYP_H_HPC_MGT_UNXWIN_HDC2`
3. **Dynamic Host Addition**: HDC2 routing configuration
4. **DNS Operations**:
   - Remove A record only (PTR management disabled)
   - No PTR record operations performed
5. **Email Notification**: Removal confirmation with PTR management disabled notice

## 4. Enterprise Automation Integration

### Example 4.1: Full Runtime Variables Integration

**AAP Extra Variables:**
```json
{
  "var_action": "add",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "autoserver01",
  "ipaddress": "*************",
  "FQDN": "autoserver01.hcloud.healthgrp.com.sg",
  "PRD_IP": "*************",
  "Environment": "production",
  "dc_location": "HDC1",
  "var_environment": "production",
  "var_sr_number": "SCR-12351",
  "ttl": "3600",
  "manage_ptr": "true"
}
```

**Expected Execution Flow:**
1. **Enhanced Variable Processing**: Full runtime variable integration
2. **Domain Analysis**: Standard ADMT server selection
3. **Instance Group Selection**: HDC1 routing with runtime variable context
4. **Dynamic Host Addition**: 
   ```yaml
   FQDN: "autoserver01.hcloud.healthgrp.com.sg"
   PRD_IP: "*************"
   Environment: "production"
   dc_location: "HDC1"
   ```
5. **DNS Operations**: Enhanced with enterprise automation context
6. **Email Notification**: Comprehensive notification with all runtime variable information

## 5. Testing and Development

### Example 5.1: Testing Mode

**AAP Extra Variables:**
```json
{
  "var_action": "verify",
  "domain": "devhealthgrp.com.sg",
  "hostname": "testserver01",
  "testing_mode": "true",
  "var_sr_number": "SCR-TEST-001",
  "var_environment": "development"
}
```

**Expected Execution Flow:**
1. **Testing Mode Activation**: Email notifications sent only to test recipients
2. **Development Environment**: Enhanced logging for testing scenarios
3. **Standard Routing**: Normal ADMT server selection and Instance Group routing

## 6. Error Scenarios and Handling

### Example 6.1: Invalid Domain

**AAP Extra Variables:**
```json
{
  "var_action": "verify",
  "domain": "invalid.domain.com",
  "hostname": "testserver01",
  "var_sr_number": "SCR-12352"
}
```

**Expected Execution Flow:**
1. **Domain Validation**: Fails at domain lookup in `admt_servers_map`
2. **Error Handling**: Graceful failure with detailed error message
3. **Email Notification**: Error notification with troubleshooting guidance

### Example 6.2: Missing IP Address for Add Operation

**AAP Extra Variables:**
```json
{
  "var_action": "add",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "newserver01",
  "var_sr_number": "SCR-12353"
}
```

**Expected Execution Flow:**
1. **Variable Validation**: Fails at IP address requirement check
2. **Error Handling**: Clear error message about missing IP address
3. **Email Notification**: Error notification with required variable information

## 7. Instance Group Routing Verification

### Monitoring Instance Group Assignment

**Key Log Entries to Monitor:**
```
TASK [Display Instance Group routing information]
ok: [localhost] => {
    "msg": [
        "=== AAP Instance Group Routing Information ===",
        "Selected ADMT Server: HISADMTVPSEC05.healthgrp.com.sg",
        "Assigned Instance Group: SYP_H_HPC_MGT_UNXWIN_HDC2",
        "Network Segment: HDC2",
        "Target Domain: healthgrp.com.sg",
        "DNS Operation: add",
        "Target: newserver01.healthgrp.com.sg",
        "=============================================="
    ]
}
```

### Email Notification Content

**Enhanced Email Sections:**
- **AAP Instance Group Routing Information**: Shows ADMT server, Instance Group, and DC location
- **Operation Summary**: Includes routing context in success/failure messages
- **Technical Details**: Enhanced with Instance Group and routing information

## 8. Performance Considerations

### Resource Usage per Operation

| Operation | Memory Impact | CPU Impact | Network Calls | Execution Time |
|-----------|---------------|------------|---------------|----------------|
| Dynamic Host Addition | ~2-3KB | Minimal | 0 | ~50-100ms |
| Instance Group Routing | Negligible | Minimal | 0 | ~10-20ms |
| DNS Operations | Standard | Standard | Standard | Standard |
| **Total Overhead** | **~3KB** | **Minimal** | **0** | **~100ms** |

### Scalability Benefits

- **Reduced Network Latency**: Optimal Instance Group routing
- **Improved Reliability**: Automatic routing to accessible network segments
- **Enhanced Monitoring**: Comprehensive execution tracking
- **Enterprise Ready**: Designed for large-scale automation scenarios

---

**DNS Management v2 - AAP Execution Examples**  
*Version 2.0.0*  
*CES Operational Excellence Team*
