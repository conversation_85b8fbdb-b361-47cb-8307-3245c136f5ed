---
# =========================================================================
# Enhanced DNS Management Operations v2 - With AAP Instance Group Routing
# =========================================================================
# This file contains all the tasks for DNS record management operations v2
# It handles the following operations with enhanced routing:
# - Dynamic ADMT server validation and connectivity
# - PowerShell script deployment and execution
# - Enhanced error handling and logging
# - Email notification with Instance Group information
# - Cleanup operations
#
# Enhanced Features v2:
# - Works with dynamically added ADMT servers from add_host
# - AAP Instance Group routing validation
# - Enterprise automation variable integration
# - Modern Ansible practices with FQCN compatibility
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

- name: Validate ADMT server was dynamically added to inventory
  ansible.builtin.assert:
    that:
      - inventory_hostname is defined
      - instance_group is defined
      - target_domain is defined
      - dns_action is defined
    fail_msg: "ADMT server not properly added to inventory or missing required variables"
    success_msg: "ADMT server {{ inventory_hostname }} validated with Instance Group {{ instance_group }}"
  run_once: true

- name: Display ADMT server and Instance Group routing information
  ansible.builtin.debug:
    msg:
      - "=== Enhanced DNS Operations v2 ==="
      - "ADMT Server: {{ inventory_hostname }}"
      - "Instance Group: {{ instance_group }}"
      - "DC Location: {{ dc_location }}"
      - "Target Domain: {{ target_domain }}"
      - "DNS Action: {{ dns_action }}"
      - "Target Host: {{ target_hostname }}.{{ target_domain }}"
      - "Target IP: {{ target_ip | default('N/A') }}"
      - "Manage PTR: {{ target_manage_ptr }}"
      - "================================="
  run_once: true
  tags: ['info']

- name: Test connectivity to ADMT server
  ansible.builtin.wait_for:
    host: "{{ inventory_hostname }}"
    port: 5985  # WinRM HTTP port
    timeout: 30
    delay: 2
  delegate_to: localhost
  run_once: true

- name: Verify WinRM connectivity to ADMT server
  ansible.windows.win_ping:
  register: winrm_test
  run_once: true

- name: Log successful ADMT server connectivity
  ansible.builtin.lineinfile:
    path: "/tmp/ansible-dns-v2.log"
    line: "{{ ansible_date_time.iso8601 }} - Successfully connected to {{ inventory_hostname }} via WinRM"
  delegate_to: localhost
  run_once: true

- name: Ensure C:\ansscripts directory exists on ADMT server
  ansible.windows.win_file:
    path: C:\ansscripts
    state: directory
  run_once: true

- name: Deploy PowerShell DNS script to ADMT server
  ansible.builtin.copy:
    src: "{{ playbook_dir }}/manage-dns/files/set-dns.ps1"
    dest: C:\ansscripts\set-dns.ps1
  run_once: true

- name: Execute DNS PowerShell script on ADMT server
  ansible.windows.win_shell: |
    PowerShell.exe -ExecutionPolicy bypass `
    -File "C:\ansscripts\set-dns.ps1" `
    -Action {{ dns_action }} `
    -Domain {{ target_domain }} `
    -HostName {{ target_hostname }} `
    {% if target_ip is defined and target_ip != '' %} -IpAddress {{ target_ip }}{% endif %} `
    -TTL {{ target_ttl | default('3600') }} `
    -ManagePtr "{{ target_manage_ptr | default(true) | string | lower }}"
  args:
    chdir: C:\ansscripts\
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  vars:
    ansible_become_user: "{{ ansible_user }}"
    ansible_become_password: "{{ ansible_password }}"
  register: dns_script_output
  ignore_errors: true
  run_once: true

- name: Log DNS script execution
  ansible.builtin.lineinfile:
    path: "/tmp/ansible-dns-v2.log"
    line: "{{ ansible_date_time.iso8601 }} - DNS script executed on {{ inventory_hostname }} for {{ target_hostname }}.{{ target_domain }} ({{ dns_action }})"
  delegate_to: localhost
  run_once: true

- name: Debug DNS Script Output
  ansible.builtin.debug:
    var: dns_script_output
  run_once: true
  tags: ['debug']

- name: Extract raw text and JSON from script output
  ansible.builtin.set_fact:
    raw_text: "{{ dns_script_output.stdout | regex_replace('({.*})', '') | trim }}"
    json_part: "{{ dns_script_output.stdout | regex_search('({.*})') }}"
  run_once: true
  when: dns_script_output.stdout is defined

- name: Debug JSON extraction
  ansible.builtin.debug:
    msg:
      - "Raw stdout: {{ dns_script_output.stdout | default('No output') }}"
      - "Extracted JSON: {{ json_part | default('No JSON found') }}"
      - "JSON type: {{ json_part | type_debug if json_part is defined else 'undefined' }}"
  run_once: true
  tags: ['debug']

- name: Parse the JSON output safely
  ansible.builtin.set_fact:
    dns_records: "{{ json_part | from_json }}"
  run_once: true
  when:
    - json_part is defined
    - json_part != ""
    - json_part is string

- name: Use JSON part directly if already parsed
  ansible.builtin.set_fact:
    dns_records: "{{ json_part }}"
  run_once: true
  when:
    - json_part is defined
    - json_part != ""
    - json_part is not string

- name: Set fallback DNS records when JSON parsing fails
  ansible.builtin.set_fact:
    dns_records:
      action: "{{ dns_action }}"
      status: "Script execution failed"
      domain: "{{ target_domain }}"
      hostname: "{{ target_hostname }}"
      ip_address: "{{ target_ip | default('') }}"
      errors: "Failed to parse script output or no JSON output received"
      ptr_management_enabled: "{{ target_manage_ptr }}"
      ptr_zone_detected: "Unknown"
      ptr_operation_status: "Script execution failed"
      admt_server: "{{ inventory_hostname }}"
      instance_group: "{{ instance_group }}"
      dc_location: "{{ dc_location }}"
  run_once: true
  when: dns_records is not defined

- name: Enhance DNS records with Instance Group information
  ansible.builtin.set_fact:
    dns_records: "{{ dns_records | combine({
      'admt_server': inventory_hostname,
      'instance_group': instance_group,
      'dc_location': dc_location,
      'execution_timestamp': ansible_date_time.iso8601
    }) }}"
  run_once: true
  when: dns_records is defined

- name: Send enhanced email notification with Instance Group information
  community.general.mail:
    host: asmtp.hcloud.healthgrp.com.sg
    port: 25
    from: "<EMAIL>"
    to: "<EMAIL>"
    bcc: '<EMAIL>'
    subject: "{{ hostvars['localhost']['var_ticket'] }} - VM Lifecycle DNS v2 Record in {{ target_domain }} [{{ dns_action }}] - {{ instance_group }}"
    body: "{{ email_template }}"
    subtype: html
  delegate_to: localhost
  when: dns_script_output is defined
  run_once: true
  notify: Update Jira Ticket

- name: Clean up PowerShell script from ADMT server
  ansible.windows.win_file:
    path: C:\ansscripts\set-dns.ps1
    state: absent
  run_once: true

- name: Remove C:\ansscripts directory if empty
  ansible.windows.win_shell: |
    if (!(Get-ChildItem -Path C:\ansscripts\)) {
        Remove-Item -Path C:\ansscripts\ -Force
    }
  run_once: true

- name: Final logging for DNS operation completion
  ansible.builtin.lineinfile:
    path: "/tmp/ansible-dns-v2.log"
    line: "{{ ansible_date_time.iso8601 }} - Completed DNS v2 operation {{ dns_action }} for {{ target_hostname }}.{{ target_domain }} on {{ inventory_hostname }}"
  delegate_to: localhost
  run_once: true

- name: Trigger Jira ticket update after DNS v2 operations completion
  ansible.builtin.debug:
    msg: "DNS v2 operations completed. Triggering Jira ticket update for {{ hostvars['localhost']['var_sr_number'] | default('SCR-XXXXX') }}"
  delegate_to: localhost
  run_once: true
  notify: Update Jira Ticket
  when:
    - hostvars['localhost']['var_sr_number'] is defined
    - hostvars['localhost']['var_sr_number'] != ""
    - hostvars['localhost']['var_sr_number'] != "SCR-XXXXX"
    - hostvars['localhost']['var_grid_id'] is defined
    - hostvars['localhost']['var_row_id'] is defined
