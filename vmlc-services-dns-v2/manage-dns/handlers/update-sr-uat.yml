---
# =========================================================================
# Jira Service Request Update Handler v2
# =========================================================================
# This handler updates the Jira Service Request ticket with the AAP job link
# It is triggered after the DNS operations are completed
# Enhanced for v2 with Instance Group routing information
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

- name: Include Jira vars for v2
  ansible.builtin.include_vars: uat_vars.yml
  no_log: false
  run_once: true

- name: Update grid row in a SR ticket with v2 information
  ansible.builtin.uri:
    url: "{{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
    headers:
      Authorization: "{{ jira_grid }}"
    validate_certs: no
    method: PUT
    status_code: 204
    body_format: json
    body: |
      {
        "rows":[
          {
            "rowId":"{{ var_row_id }}",
            "columns":{
              "remark": "DNS v2 - {{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/ - ADMT: {{ hostvars[groups['admt_servers'][0]]['inventory_hostname'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }} - IG: {{ hostvars[groups['admt_servers'][0]]['instance_group'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }}"
            }
          }
        ]
      }
  ignore_errors: true
  delegate_to: localhost
  run_once: true
