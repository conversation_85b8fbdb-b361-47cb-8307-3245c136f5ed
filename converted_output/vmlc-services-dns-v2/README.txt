# DNS Management v2 - Enhanced with AAP Instance Group Routing

## Overview

The DNS Management v2 project is an enhanced version of the original DNS management automation system, specifically designed to address AAP (Ansible Automation Platform) Instance Group routing challenges and network segmentation requirements. This version introduces dynamic ADMT (Active Directory Migration Tool) server selection with intelligent Instance Group routing for enterprise automation scenarios.

## Enhanced Features v2

### 🚀 **Dynamic ADMT Server Selection**
- Automatic selection of appropriate ADMT servers based on target domain
- Runtime variable integration for enterprise automation scenarios
- Intelligent mapping of domains to their corresponding ADMT servers

### 🌐 **AAP Instance Group Routing**
- Automatic routing to correct AAP Instance Groups based on network segmentation
- Support for HDC1 and HDC2 network segments with specific routing requirements
- Eliminates manual Instance Group configuration and routing errors

### 🔧 **Enterprise Automation Integration**
- Runtime variables support (FQDN, PRD_IP, Environment, dc_location)
- Dynamic host addition using `add_host` module in pre_tasks
- Seamless integration with existing enterprise automation workflows

### 📊 **Enhanced Monitoring and Logging**
- Comprehensive execution tracking with Instance Group information
- Enhanced email notifications with routing details
- Improved error handling and troubleshooting guidance

## Network Segmentation and Instance Group Mapping

### Instance Group Requirements

Due to network segmentation policies, specific ADMT servers require routing through designated AAP Instance Groups:

#### HDC2 Instance Group (`SYP_H_HPC_MGT_UNXWIN_HDC2`)
- `HISADMTVSSEC01.healthgrpexts.com.sg`
- `HISADMTVPSEC05.healthgrp.com.sg`

#### HDC1 Instance Group (`SYP_H_HPC_MGT_UNXWIN_HDC1`) - Default
- All other ADMT servers

## Architecture Overview

```
AAP Job Execution
       ↓
Dynamic ADMT Selection (based on domain)
       ↓
Instance Group Routing (based on ADMT server)
       ↓
Network Segmentation (HDC1/HDC2)
       ↓
DNS Operations on Target ADMT Server
```

## Project Structure

```
vmlc-services-dns-v2/
├── main.yml                               # Enhanced main playbook with dynamic routing
├── collections/
│   └── requirements.yml                   # Updated collection requirements
├── manage-dns/
│   ├── files/
│   │   └── set-dns.ps1                   # Enhanced PowerShell script v2
│   ├── handlers/
│   │   └── update-sr-uat.yml             # Enhanced Jira handler with v2 info
│   ├── tasks/
│   │   ├── main.yml                      # Enhanced main tasks entry point
│   │   └── dns-operations.yml            # Enhanced DNS operations with routing
│   ├── templates/
│   │   └── email_template_dns.j2         # Enhanced email template with routing info
│   └── vars/
│       ├── dns_vars.yml                  # Enhanced variables with Instance Group mapping
│       ├── prod_vars.yml                 # Production environment variables
│       └── uat_vars.yml                  # UAT environment variables
└── docs/
    ├── README.md                         # This file
    ├── USAGE.md                          # Usage instructions for v2
    ├── UPGRADE_GUIDE.md                  # Migration guide from v1 to v2
    └── TROUBLESHOOTING.md                # v2-specific troubleshooting guide
```

## Quick Start

### AAP Job Template Configuration

#### Required Extra Variables
```json
{
  "var_action": "verify|add|remove",
  "domain": "target.domain.com",
  "hostname": "target-hostname",
  "ipaddress": "192.168.1.x",
  "var_sr_number": "SCR-XXXXX",
  "var_environment": "production"
}
```

#### Example: Verify Operation
```json
{
  "var_action": "verify",
  "domain": "healthgrp.com.sg",
  "hostname": "testserver01",
  "var_sr_number": "SCR-12345",
  "var_environment": "production"
}
```

#### Example: Add Operation
```json
{
  "var_action": "add",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "newprod01",
  "ipaddress": "*************",
  "ttl": "3600",
  "manage_ptr": "true",
  "var_sr_number": "SCR-12346"
}
```

## Key Improvements Over v1

| Feature | v1 | v2 |
|---------|----|----|
| ADMT Server Selection | Static mapping in tasks | Dynamic selection in pre_tasks |
| Instance Group Routing | Manual configuration | Automatic routing based on network segmentation |
| Runtime Variables | Limited support | Full enterprise automation integration |
| Error Handling | Basic error reporting | Enhanced with routing context |
| Monitoring | Standard logging | Comprehensive execution tracking |
| Email Notifications | Basic DNS status | Enhanced with routing information |

## Supported Operations

### 1. **Verify** - DNS Record Verification
- Checks existence of A and PTR records
- Validates current DNS configuration
- Provides comprehensive status reporting

### 2. **Add** - DNS Record Creation
- Creates A records with specified IP addresses
- Intelligent PTR record management with zone detection
- Supports custom TTL values

### 3. **Remove** - DNS Record Deletion
- Safely removes A records and matching PTR records
- Preserves PTR records pointing to different hostnames
- Comprehensive safety checks

## Prerequisites

### AAP Requirements
- Ansible Automation Platform with Instance Group support
- Configured Instance Groups: `SYP_H_HPC_MGT_UNXWIN_HDC1` and `SYP_H_HPC_MGT_UNXWIN_HDC2`
- Network connectivity from Instance Groups to respective ADMT servers

### ADMT Server Requirements
- Windows PowerShell 5.1 or later
- DNS management permissions for target domains
- Network connectivity to DNS servers
- WinRM enabled for Ansible connectivity

### Ansible Collections
- `cloud_cpe.cyberark_ccp` (>=1.0.0)
- `community.general` (>=5.0.0)
- `ansible.windows` (>=1.0.0)

## Installation

1. **Deploy to AAP:**
   ```bash
   # Copy project to AAP project directory
   cp -r vmlc-services-dns-v2 /var/lib/awx/projects/
   ```

2. **Install Collections:**
   ```bash
   ansible-galaxy collection install -r collections/requirements.yml
   ```

3. **Configure AAP Job Template:**
   - Set inventory to minimal/localhost
   - Configure Instance Groups as needed
   - Set up credential management for ADMT servers

## Migration from v1

For organizations currently using vmlc-services-dns-v1, see the [UPGRADE_GUIDE.md](docs/UPGRADE_GUIDE.md) for detailed migration instructions.

## Support and Documentation

- **Usage Instructions:** [docs/USAGE.md](docs/USAGE.md)
- **Troubleshooting:** [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md)
- **Upgrade Guide:** [docs/UPGRADE_GUIDE.md](docs/UPGRADE_GUIDE.md)

## Contact Information

- **Primary Support:** CES Operational Excellence Team
- **Email:** <EMAIL>
- **Project Version:** 2.0.0
- **Last Updated:** 2024

---

**DNS Management v2** - Enhanced with AAP Instance Group Routing  
*CES Operational Excellence Team*
