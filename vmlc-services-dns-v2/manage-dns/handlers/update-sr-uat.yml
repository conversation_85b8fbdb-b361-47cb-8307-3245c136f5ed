---
# =========================================================================
# Jira Service Request Update Handler v2
# =========================================================================
# This handler updates the Jira Service Request ticket with the AAP job link
# It is triggered after the DNS operations are completed
# Enhanced for v2 with Instance Group routing information
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

- name: Check if all required Jira variables are available for v2
  ansible.builtin.set_fact:
    jira_update_enabled: >-
      {{
        (var_sr_number is defined and var_sr_number != '' and var_sr_number != 'SCR-XXXXX') and
        (var_grid_id is defined and var_grid_id != '') and
        (var_row_id is defined and var_row_id != '')
      }}
  run_once: true

- name: Determine overall job status for v2
  ansible.builtin.set_fact:
    overall_job_status: "{{ dns_operation_status | default('UNKNOWN') }}"
  run_once: true

- name: Display Jira update status for v2
  ansible.builtin.debug:
    msg: |
      Jira Update Status: {{ 'ENABLED' if jira_update_enabled else 'DISABLED' }}
      Overall Job Status: {{ overall_job_status }}
      Reason: {{ 'All required variables present' if jira_update_enabled else 'Missing required Jira variables (var_sr_number, var_grid_id, var_row_id)' }}
      SR Number: {{ var_sr_number | default('NOT_PROVIDED') }}
      Grid ID: {{ var_grid_id | default('NOT_PROVIDED') }}
      Row ID: {{ var_row_id | default('NOT_PROVIDED') }}
  run_once: true

- name: Try to include prod_vars.yml first for v2
  ansible.builtin.include_vars: ../vars/prod_vars.yml
  no_log: false
  run_once: true
  ignore_errors: true
  register: prod_vars_result
  when: jira_update_enabled

- name: Include uat_vars.yml if prod_vars.yml failed for v2
  ansible.builtin.include_vars: ../vars/uat_vars.yml
  no_log: false
  run_once: true
  when: jira_update_enabled and prod_vars_result.failed

- name: Display which vars file was loaded for v2
  ansible.builtin.debug:
    msg: "Loaded Jira vars from (v2): {{ 'prod_vars.yml' if not prod_vars_result.failed else 'uat_vars.yml' }}"
  run_once: true
  when: jira_update_enabled

- name: Test Jira grid connectivity for v2
  ansible.builtin.uri:
    url: "{{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
    headers:
      Authorization: "{{ jira_grid }}"
    validate_certs: no
    method: GET
    status_code: [200, 404]
    return_content: true
  register: jira_connectivity_test
  ignore_errors: true
  delegate_to: localhost
  run_once: true
  when: jira_update_enabled

- name: Display Jira connectivity test results for v2
  ansible.builtin.debug:
    msg:
      - "=== Jira Connectivity Test (v2) ==="
      - "Status Code: {{ jira_connectivity_test.status | default('N/A') }}"
      - "Can Connect: {{ 'YES' if jira_connectivity_test.status == 200 else 'NO' }}"
      - "Error: {{ jira_connectivity_test.msg | default('None') }}"
  run_once: true
  when: jira_update_enabled and jira_connectivity_test is defined

- name: Debug Jira API call details for v2
  ansible.builtin.debug:
    msg:
      - "=== Jira API Call Debug Information (v2) ==="
      - "URL: {{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
      - "Grid ID: {{ var_grid_id }}"
      - "SR Number: {{ var_sr_number }}"
      - "Row ID: {{ var_row_id }}"
      - "AAP URL: {{ aap_url | default('NOT_SET') }}"
      - "Tower Job ID: {{ tower_job_id | default('NOT_SET') }}"
      - "Job Status: {{ overall_job_status }}"
      - "ADMT Server: {{ hostvars[groups['admt_servers'][0]]['inventory_hostname'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }}"
      - "Instance Group: {{ hostvars[groups['admt_servers'][0]]['instance_group'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }}"
      - "Authorization Header: {{ 'SET' if jira_grid is defined else 'NOT_SET' }}"
  run_once: true
  when: jira_update_enabled

- name: Update grid row in a SR ticket with v2 information
  ansible.builtin.uri:
    url: "{{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
    headers:
      Authorization: "{{ jira_grid }}"
    validate_certs: no
    method: PUT
    status_code: [200, 204]
    body_format: json
    body: |
      {
        "rows":[
          {
            "rowId":"{{ var_row_id }}",
            "columns":{
              "remark": "DNS v2 - {{ aap_url | default('https://aap.hcc.com.sg') | regex_replace('/$', '') }}/#/jobs/playbook/{{ tower_job_id }}/ - ADMT: {{ hostvars[groups['admt_servers'][0]]['inventory_hostname'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }} - IG: {{ hostvars[groups['admt_servers'][0]]['instance_group'] if groups['admt_servers'] is defined and groups['admt_servers'] | length > 0 else 'N/A' }}",
              "status": "{{ overall_job_status }}"
            }
          }
        ]
      }
    return_content: true
  register: jira_api_response
  ignore_errors: true
  delegate_to: localhost
  run_once: true
  when: jira_update_enabled

- name: Display Jira API response for v2
  ansible.builtin.debug:
    msg:
      - "=== Jira API Response (v2) ==="
      - "Status Code: {{ jira_api_response.status | default('N/A') }}"
      - "Response Body: {{ jira_api_response.content | default('N/A') }}"
      - "Failed: {{ jira_api_response.failed | default(false) }}"
      - "Error Message: {{ jira_api_response.msg | default('None') }}"
  run_once: true
  when: jira_update_enabled and jira_api_response is defined
