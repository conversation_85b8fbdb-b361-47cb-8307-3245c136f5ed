---
# =========================================================================
# Jira Service Request Update Handler
# =========================================================================
# This handler updates the Jira Service Request ticket with the AAP job link
# It is triggered after the DNS operations are completed
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

- name: Check if all required Jira variables are available
  ansible.builtin.set_fact:
    jira_update_enabled: >-
      {{
        (var_sr_number is defined and var_sr_number != '' and var_sr_number != 'SCR-XXXXX') and
        (var_grid_id is defined and var_grid_id != '') and
        (var_row_id is defined and var_row_id != '')
      }}
  run_once: true

- name: Determine overall job status
  ansible.builtin.set_fact:
    overall_job_status: "{{ dns_operation_status | default('UNKNOWN') }}"
  run_once: true

- name: Display Jira update status
  ansible.builtin.debug:
    msg: |
      Jira Update Status: {{ 'ENABLED' if jira_update_enabled else 'DISABLED' }}
      Overall Job Status: {{ overall_job_status }}
      Reason: {{ 'All required variables present' if jira_update_enabled else 'Missing required Jira variables (var_sr_number, var_grid_id, var_row_id)' }}
      SR Number: {{ var_sr_number | default('NOT_PROVIDED') }}
      Grid ID: {{ var_grid_id | default('NOT_PROVIDED') }}
      Row ID: {{ var_row_id | default('NOT_PROVIDED') }}
  run_once: true

- name: Include Jira vars
  ansible.builtin.include_vars: uat_vars.yml
  no_log: false
  run_once: true
  when: jira_update_enabled

- name: Update grid row in a SR ticket
  ansible.builtin.uri:
    url: "{{ grid_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
    headers:
      Authorization: "{{ jira_grid }}"
    validate_certs: no
    method: PUT
    status_code: 204
    body_format: json
    body: |
      {
        "rows":[
          {
            "rowId":"{{ var_row_id }}",
            "columns":{
              "remark": "{{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/",
              "status": "{{ overall_job_status }}"
            }
          }
        ]
      }
  ignore_errors: true
  delegate_to: localhost
  run_once: true
  when: jira_update_enabled