# DNS Management v2 - Usage Guide

## Overview

This guide provides comprehensive instructions for using the DNS Management v2 system with enhanced AAP Instance Group routing capabilities. The v2 system introduces dynamic ADMT server selection and intelligent Instance Group routing for enterprise automation scenarios.

## Key Concepts

### Dynamic ADMT Server Selection
The v2 system automatically selects the appropriate ADMT server based on the target domain, eliminating the need for manual server specification.

### Instance Group Routing
AAP Instance Groups are automatically assigned based on network segmentation requirements:
- **HDC2 Instance Group**: `SYP_H_HPC_MGT_UNXWIN_HDC2`
- **HDC1 Instance Group**: `SYP_H_HPC_MGT_UNXWIN_HDC1` (default)

### Runtime Variables
The system supports enterprise automation runtime variables:
- `FQDN`: Fully Qualified Domain Name
- `PRD_IP`: Production IP Address  
- `Environment`: Target environment (production, staging, development)
- `dc_location`: Data center location (HDC1, HDC2)

## AAP Job Template Configuration

### Basic Setup

1. **Create Job Template in AAP**
   - Name: `DNS Management v2`
   - Project: `vmlc-services-dns-v2`
   - Playbook: `main.yml`
   - Inventory: Use minimal inventory (localhost only)

2. **Configure Instance Groups**
   - Ensure both `SYP_H_HPC_MGT_UNXWIN_HDC1` and `SYP_H_HPC_MGT_UNXWIN_HDC2` are configured
   - Verify network connectivity from Instance Groups to respective ADMT servers

### Required Extra Variables

#### Minimum Required Variables
```json
{
  "var_action": "verify|add|remove",
  "domain": "target.domain.com",
  "hostname": "target-hostname"
}
```

#### Complete Variable Set
```json
{
  "var_action": "verify|add|remove",
  "domain": "target.domain.com", 
  "hostname": "target-hostname",
  "ipaddress": "192.168.1.x",
  "ttl": "3600",
  "manage_ptr": "true",
  "var_sr_number": "SCR-XXXXX",
  "var_environment": "production"
}
```

## Operation Examples

### 1. Verify DNS Records

#### Basic Verification
```json
{
  "var_action": "verify",
  "domain": "healthgrp.com.sg",
  "hostname": "testserver01",
  "var_sr_number": "SCR-12345"
}
```

**Expected Behavior:**
- Domain `healthgrp.com.sg` → Selects `HISADMTVPSEC05.healthgrp.com.sg`
- ADMT server → Routes to `SYP_H_HPC_MGT_UNXWIN_HDC2` Instance Group
- Verifies A and PTR records for `testserver01.healthgrp.com.sg`

#### Verification with Environment Context
```json
{
  "var_action": "verify",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "prodserver01",
  "var_environment": "production",
  "var_sr_number": "SCR-12346"
}
```

### 2. Add DNS Records

#### Basic Add Operation
```json
{
  "var_action": "add",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "newserver01",
  "ipaddress": "*************",
  "var_sr_number": "SCR-12347"
}
```

**Expected Behavior:**
- Domain `hcloud.healthgrp.com.sg` → Selects `HISADMTVPSEC06.hcloud.healthgrp.com.sg`
- ADMT server → Routes to `SYP_H_HPC_MGT_UNXWIN_HDC1` Instance Group
- Creates A record: `newserver01.hcloud.healthgrp.com.sg` → `*************`
- Creates corresponding PTR record (if zone available)

#### Add with Custom TTL and PTR Management
```json
{
  "var_action": "add",
  "domain": "healthgrpexts.com.sg",
  "hostname": "specialserver01",
  "ipaddress": "************",
  "ttl": "7200",
  "manage_ptr": "true",
  "var_environment": "production",
  "var_sr_number": "SCR-12348"
}
```

**Expected Behavior:**
- Domain `healthgrpexts.com.sg` → Selects `HISADMTVSSEC01.healthgrpexts.com.sg`
- ADMT server → Routes to `SYP_H_HPC_MGT_UNXWIN_HDC2` Instance Group
- Creates A record with 7200 second TTL
- Intelligent PTR zone detection and record creation

### 3. Remove DNS Records

#### Basic Remove Operation
```json
{
  "var_action": "remove",
  "domain": "nhg.local",
  "hostname": "oldserver01",
  "var_sr_number": "SCR-12349"
}
```

**Expected Behavior:**
- Domain `nhg.local` → Selects `HISADMTVPSEC11.nhg.local`
- ADMT server → Routes to `SYP_H_HPC_MGT_UNXWIN_HDC1` Instance Group
- Removes A record for `oldserver01.nhg.local`
- Safely removes matching PTR records only

#### Remove with PTR Management Disabled
```json
{
  "var_action": "remove",
  "domain": "ses.shsu.com.sg",
  "hostname": "legacyserver01",
  "manage_ptr": "false",
  "var_sr_number": "SCR-12350"
}
```

## Domain to ADMT Server Mapping

### Automatic Selection Logic

| Domain | ADMT Server | Instance Group | DC Location |
|--------|-------------|----------------|-------------|
| `devhealthgrp.com.sg` | `HISADMTVDSEC01.devhealthgrp.com.sg` | HDC1 | HDC1 |
| `healthgrpexts.com.sg` | `HISADMTVSSEC01.healthgrpexts.com.sg` | **HDC2** | HDC2 |
| `nnstg.local` | `HISADMTVSSEC02.nnstg.local` | HDC1 | HDC1 |
| `ses.shsu.com.sg` | `SHSADMTVDSEC02.ses.shsu.com.sg` | HDC1 | HDC1 |
| `shses.shs.com.sg` | `SHSADMTVPSEC12.shses.shs.com.sg` | HDC1 | HDC1 |
| `nhg.local` | `HISADMTVPSEC11.nhg.local` | HDC1 | HDC1 |
| `aic.local` | `HISADMTVPSEC02.aic.local` | HDC1 | HDC1 |
| `iltc.healthgrp.com.sg` | `HISADMTVPSEC04.iltc.healthgrp.com.sg` | HDC1 | HDC1 |
| `healthgrp.com.sg` | `HISADMTVPSEC05.healthgrp.com.sg` | **HDC2** | HDC2 |
| `hcloud.healthgrp.com.sg` | `HISADMTVPSEC06.hcloud.healthgrp.com.sg` | HDC1 | HDC1 |
| `healthgrpextp.com.sg` | `HISADMTVPSEC08.healthgrpextp.com.sg` | HDC1 | HDC1 |

**Note**: HDC2 Instance Group servers are highlighted in bold.

## Advanced Usage

### Enterprise Automation Integration

#### With Full Runtime Variables
```json
{
  "var_action": "add",
  "domain": "hcloud.healthgrp.com.sg",
  "hostname": "autoserver01",
  "ipaddress": "*************",
  "FQDN": "autoserver01.hcloud.healthgrp.com.sg",
  "PRD_IP": "*************",
  "Environment": "production",
  "dc_location": "HDC1",
  "var_environment": "production",
  "var_sr_number": "SCR-12351"
}
```

#### Testing Mode
```json
{
  "var_action": "verify",
  "domain": "devhealthgrp.com.sg",
  "hostname": "testserver01",
  "testing_mode": "true",
  "var_sr_number": "SCR-TEST-001"
}
```

### PTR Management Options

#### Enable PTR Management (Default)
```json
{
  "manage_ptr": "true"
}
```

#### Disable PTR Management
```json
{
  "manage_ptr": "false"
}
```

## Monitoring and Troubleshooting

### Job Execution Monitoring

1. **AAP Job Logs**: Monitor Instance Group routing decisions
2. **Email Notifications**: Enhanced notifications with routing information
3. **Execution Logs**: Check `/tmp/ansible-dns-v2.log` on execution nodes

### Common Troubleshooting Scenarios

#### Instance Group Routing Issues
- Verify Instance Group configuration in AAP
- Check network connectivity from Instance Groups to ADMT servers
- Review Instance Group assignment in job logs

#### ADMT Server Selection Issues
- Verify domain exists in `admt_servers_map`
- Check ADMT server accessibility
- Review dynamic host addition logs

#### PTR Zone Detection Issues
- Check PTR zone availability
- Verify network connectivity to PTR DNS servers
- Consider disabling PTR management for A-record-only operations

## Best Practices

### Variable Management
- Always specify `var_sr_number` for tracking
- Use consistent `var_environment` values
- Include `testing_mode: true` for non-production testing

### Error Handling
- Monitor email notifications for operation status
- Review AAP job logs for detailed execution information
- Use verify operations before add/remove for validation

### Performance Optimization
- Leverage Instance Group routing for optimal network performance
- Use appropriate TTL values for DNS records
- Monitor ADMT server performance and availability

## Migration from v1

### Key Differences
- **No Manual ADMT Server Selection**: v2 automatically selects servers
- **Instance Group Routing**: Automatic routing based on network segmentation
- **Enhanced Variables**: Additional runtime variables for enterprise automation
- **Improved Monitoring**: Enhanced logging and notification capabilities

### Migration Steps
1. Update AAP job templates to use v2 project
2. Remove manual ADMT server specifications from variables
3. Verify Instance Group configurations
4. Test with non-production domains first
5. Monitor routing effectiveness and performance

## Support and Contact

- **Primary Support**: CES Operational Excellence Team
- **Email**: <EMAIL>
- **Documentation**: DNS Management v2 Project
- **Emergency Escalation**: Follow standard IT support procedures

---

**DNS Management v2 - Usage Guide**  
*Version 2.0.0*  
*CES Operational Excellence Team*
