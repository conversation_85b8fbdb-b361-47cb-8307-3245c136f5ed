---
# =========================================================================
# DNS Management Tasks
# =========================================================================
# This file contains all the tasks for DNS record management operations
# It handles the following operations:
# - Server selection based on domain
# - Credential selection based on domain
# - PowerShell script deployment and execution
# - Email notification
# - Cleanup
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# =========================================================================

- name: Defining ADMT servers mapping
  ansible.builtin.set_fact:
    admt_servers_map:
      devhealthgrp.com.sg: ["HISADMTVDSEC01.devhealthgrp.com.sg"]
      healthgrpexts.com.sg: ["HISADMTVSSEC01.healthgrpexts.com.sg"]
      nnstg.local: ["HISADMTVSSEC02.nnstg.local"]
      ses.shsu.com.sg: ["SHSADMTVDSEC02.ses.shsu.com.sg"]
      shses.shs.com.sg: ["SHSADMTVPSEC12.shses.shs.com.sg"]
      nhg.local: ["HISADMTVPSEC11.nhg.local"]
      aic.local: ["HISADMTVPSEC02.aic.local"]
      iltc.healthgrp.com.sg: ["HISADMTVPSEC04.iltc.healthgrp.com.sg"]
      healthgrp.com.sg: ["HISADMTVPSEC05.healthgrp.com.sg"]
      hcloud.healthgrp.com.sg: ["HISADMTVPSEC06.hcloud.healthgrp.com.sg"]
      healthgrpextp.com.sg: ["HISADMTVPSEC08.healthgrpextp.com.sg"]
  run_once: true

- name: Selecting ADMT servers based on defined domain using dictionary lookup
  ansible.builtin.set_fact:
    admt_servers: "{{ admt_servers_map[domain] | default([]) }}"
  when: domain is defined
  run_once: true

- name: Selecting Credentials based on defined domain
  ansible.builtin.set_fact:
    ansible_user: "{{ var_dns_devhealthgrp_username if domain == 'devhealthgrp.com.sg' else
      var_dns_healthgrpexts_username if domain == 'healthgrpexts.com.sg' else
      var_dns_nnstg_username if domain == 'nnstg.local' else
      var_dns_ses_username if domain == 'ses.shsu.com.sg' else
      var_dns_shses_username if domain == 'shses.shs.com.sg' else
      var_dns_nhg_username if domain == 'nhg.local' else
      var_dns_aic_username if domain == 'aic.local' else
      var_dns_iltc_username if domain == 'iltc.healthgrp.com.sg' else
      var_dns_healthgrp_username if domain == 'healthgrp.com.sg' else
      var_dns_hcloud_username if domain == 'hcloud.healthgrp.com.sg' else
      var_dns_healthgrpextp_username if domain == 'healthgrpextp.com.sg' }}"
    ansible_password: "{{ var_dns_devhealthgrp_password if domain == 'devhealthgrp.com.sg' else
      var_dns_healthgrpexts_password if domain == 'healthgrpexts.com.sg' else
      var_dns_nnstg_password if domain == 'nnstg.local' else
      var_dns_ses_password if domain == 'ses.shsu.com.sg' else
      var_dns_shses_password if domain == 'shses.shs.com.sg' else
      var_dns_nhg_password if domain == 'nhg.local' else
      var_dns_aic_password if domain == 'aic.local' else
      var_dns_iltc_password if domain == 'iltc.healthgrp.com.sg' else
      var_dns_healthgrp_password if domain == 'healthgrp.com.sg' else
      var_dns_hcloud_password if domain == 'hcloud.healthgrp.com.sg' else
      var_dns_healthgrpextp_password if domain == 'healthgrpextp.com.sg' }}"
  no_log: true
  run_once: true

- name: Checking if C:\ansscripts directory exists on {{ admt_servers }}
  ansible.windows.win_file:
    path: C:\ansscripts
    state: directory
  delegate_to: "{{ item }}"
  loop: "{{ admt_servers }}"
  run_once: true

- name: Preparing the plugins on {{ admt_servers }}
  ansible.builtin.copy:
    src: "{{ playbook_dir }}/manage-dns/files/set-dns.ps1"
    dest: C:\ansscripts\set-dns.ps1
  delegate_to: "{{ item }}"
  loop: "{{ admt_servers }}"
  run_once: true

- name: Starting plugin block {{ var_action | default('verify') }} on {{ admt_servers }}
  ansible.windows.win_shell: |
    PowerShell.exe -ExecutionPolicy bypass `
    -File "C:\ansscripts\set-dns.ps1" `
    -Action {{ var_action | default('verify') }} `
    -Domain {{ domain }} `
    -HostName {{ hostname }} `
    {% if ipaddress is defined and ipaddress|length > 0 %} -IpAddress {{ ipaddress }}{% endif %} `
    -TTL {{ ttl | default('3600') }} `
    -ManagePtr "{{ manage_ptr | default(true) | string | lower }}"
  args:
    chdir: C:\ansscripts\
  become: true
  become_method: runas
  become_flags: logon_type=new_credentials logon_flags=netcredentials_only
  delegate_to: "{{ item }}"
  loop: "{{ admt_servers }}"
  run_once: true
  vars:
    ansible_become_user: "{{ ansible_user }}"
    ansible_become_password: "{{ ansible_password }}"
  register: dns_script_output
  ignore_errors: true

- name: Debug Script Output
  ansible.builtin.debug:
    var: dns_script_output
  run_once: true

- name: Extract raw text and JSON separately
  ansible.builtin.set_fact:
    raw_text: "{{ dns_script_output.results[0].stdout | regex_replace('({.*})', '') | trim }}"
    json_part: "{{ dns_script_output.results[0].stdout | regex_search('({.*})') }}"
  run_once: true

- name: Debug JSON extraction
  ansible.builtin.debug:
    msg:
      - "Raw stdout: {{ dns_script_output.results[0].stdout }}"
      - "Extracted JSON: {{ json_part }}"
      - "JSON type: {{ json_part | type_debug }}"
  run_once: true

- name: Parse the JSON output safely
  ansible.builtin.set_fact:
    dns_records: "{{ json_part | from_json }}"
  run_once: true
  when:
    - json_part is defined
    - json_part != ""
    - json_part is string

- name: Use JSON part directly if already parsed
  ansible.builtin.set_fact:
    dns_records: "{{ json_part }}"
  run_once: true
  when:
    - json_part is defined
    - json_part != ""
    - json_part is not string

- name: Set fallback DNS records when JSON parsing fails
  ansible.builtin.set_fact:
    dns_records:
      action: "{{ var_action | default('verify') }}"
      status: "Script execution failed"
      domain: "{{ domain }}"
      hostname: "{{ hostname }}"
      ip_address: "{{ ipaddress | default('') }}"
      errors: "Failed to parse script output or no JSON output received"
      ptr_management_enabled: "{{ manage_ptr | default(true) }}"
      ptr_zone_detected: "Unknown"
      ptr_operation_status: "Script execution failed"
  run_once: true
  when: dns_records is not defined

- name: Triggering notifications to relevant teams
  community.general.mail:
    host: asmtp.hcloud.healthgrp.com.sg
    port: 25
    from: "<EMAIL>"
    to: "<EMAIL>"
#    to: >-
#      {{
#        '<EMAIL>' if domain in ['ses.shsu.com.sg', 'shses.shs.com.sg'] else
#        '<EMAIL>'
#      }}
    bcc: '<EMAIL>'
    subject: " {{ var_ticket }} - VM Lifecycle Non-Windows DNS A Record in {{ domain }} [{{ var_action | default('verify') }}]"
    body: "{{ email_template }}"
    subtype: html
  delegate_to: localhost
  when: dns_script_output is defined
  run_once: true
  notify: Update Jira Ticket

- name: Cleaning up the PowerShell script from {{ admt_servers }}
  ansible.windows.win_file:
    path: C:\ansscripts\set-dns.ps1
    state: absent
  delegate_to: "{{ item }}"
  loop: "{{ admt_servers }}"
  run_once: true

- name: Removing C:\ansscripts directory from {{ admt_servers }}
  ansible.windows.win_shell: |
    if (!(Get-ChildItem -Path C:\ansscripts\)) {
        Remove-Item -Path C:\ansscripts\ -Force
    }
  delegate_to: "{{ item }}"
  loop: "{{ admt_servers }}"
  run_once: true

- name: Trigger Jira ticket update after DNS operations completion
  ansible.builtin.debug:
    msg: "DNS operations completed. Triggering Jira ticket update for {{ var_ticket | default('SCR-XXXXX') }}"
  run_once: true
  notify: Update Jira Ticket
  when: var_sr_number is defined and var_sr_number != ""